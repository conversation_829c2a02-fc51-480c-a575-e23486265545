# SIP 功能实现文档

本文档描述了使用 panjjo/gosip 库实现的三个主要 SIP 功能。

## 功能概述

1. **SIP MESSAGE 请求 - 查询目录**：用于向平台查询设备列表
2. **SIP INVITE 请求 - 推流**：用于请求设备推送视频流到指定位置
3. **SIP MESSAGE 请求 - PTZ 控制**：用于控制摄像头的云台操作

## 实现详情

### 1. SIP MESSAGE 请求 - 查询目录

#### 功能描述
向已注册的平台发送目录查询请求，获取该平台下的所有设备列表。

#### 实现方法
```go
func (srv *Server) SendCatalogQuery(platformID string) (string, error)
```

#### 实现特点
- 使用 XML 格式的消息体，包含 CmdType="Catalog"
- 生成唯一的 SN（序列号）用于响应匹配
- 使用 SIP MESSAGE 方法发送请求
- 异步等待响应处理

#### XML 消息格式
```xml
<Query>
    <CmdType>Catalog</CmdType>
    <SN>12345678</SN>
    <DeviceID>34020000001320000001</DeviceID>
</Query>
```

### 2. SIP INVITE 请求 - 推流

#### 功能描述
向指定设备发送 INVITE 请求，要求设备将视频流推送到指定的 IP 地址和端口。

#### 实现方法
```go
func (srv *Server) SendInvite(gbID, receiveIP string, receivePort int) (*models.StreamSession, error)
```

#### 实现特点
- 使用 SDP（Session Description Protocol）描述媒体会话
- 支持多种视频编码格式（PS、H264、MPEG4）
- 生成唯一的 SSRC 和会话 ID
- 发送 ACK 确认响应

#### SDP 消息格式
```
v=0
o=34020000002000000001 0 0 IN IP4 *************
s=Play
c=IN IP4 *************
t=0 0
m=video 8000 RTP/AVP 96 98 97
a=recvonly
a=rtpmap:96 PS/90000
a=rtpmap:98 H264/90000
a=rtpmap:97 MPEG4/90000
```

### 3. SIP MESSAGE 请求 - PTZ 控制

#### 功能描述
向指定设备发送云台控制命令，支持上下左右移动和停止操作。

#### 实现方法
```go
func (srv *Server) SendPTZControl(gbID, command string, speed int) error
```

#### 支持的命令
- `up`: 向上移动
- `down`: 向下移动  
- `left`: 向左移动
- `right`: 向右移动
- `stop`: 停止移动

#### PTZ 命令格式
PTZ 命令使用十六进制字符串格式：`A50F01{direction}{speed}00{checksum}`

- Direction codes:
  - up: 08
  - down: 04
  - left: 02
  - right: 01
  - stop: 00
- Speed: 00-FF (0-255)
- Checksum: XOR 校验和

#### XML 消息格式
```xml
<Control>
    <CmdType>DeviceControl</CmdType>
    <SN>12345678</SN>
    <DeviceID>34020000001320000002</DeviceID>
    <PTZCmd>A50F0108320091</PTZCmd>
    <Info>
        <ControlPriority>5</ControlPriority>
    </Info>
</Control>
```

## 使用示例

### 查询设备目录
```go
// 向平台查询设备列表
sn, err := server.SendCatalogQuery("34020000001320000001")
if err != nil {
    log.Printf("Failed to send catalog query: %v", err)
    return
}
log.Printf("Catalog query sent with SN: %s", sn)
```

### 请求视频推流
```go
// 请求设备推流到指定地址
session, err := server.SendInvite("34020000001320000002", "*************", 8000)
if err != nil {
    log.Printf("Failed to send INVITE: %v", err)
    return
}
log.Printf("Stream session created: %s", session.SessionID)
```

### 控制云台移动
```go
// 控制摄像头向上移动，速度为 50
err := server.SendPTZControl("34020000001320000002", "up", 50)
if err != nil {
    log.Printf("Failed to send PTZ control: %v", err)
    return
}

// 停止移动
err = server.SendPTZControl("34020000001320000002", "stop", 0)
if err != nil {
    log.Printf("Failed to stop PTZ: %v", err)
    return
}
```

## 依赖库

- `github.com/panjjo/gosip`: SIP 协议实现
- `github.com/panjjo/gosdp`: SDP 协议实现

## 注意事项

1. 所有 SIP 请求都是异步发送，响应通过回调处理
2. 需要确保平台和设备已正确注册到系统中
3. PTZ 控制命令的格式可能因设备厂商而异
4. 视频流的编码格式需要与接收端兼容
5. 网络连接和防火墙设置可能影响 SIP 通信

## 测试

运行测试验证 PTZ 命令生成功能：
```bash
go test ./internal/sip -v -run="TestCreatePTZCommand"
```

## 扩展功能

未来可以扩展的功能：
- 支持更多 PTZ 控制命令（缩放、聚焦等）
- 实现录像回放功能
- 支持音频流传输
- 添加设备状态查询功能
